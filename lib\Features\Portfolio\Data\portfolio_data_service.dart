import 'package:dartz/dartz.dart';
import 'package:devfolio/Core/models/certification_model.dart';
import 'package:devfolio/Core/models/education_model.dart';
import 'package:devfolio/Core/models/experience_model.dart';
import 'package:devfolio/Core/models/personal_info_model.dart';
import 'package:devfolio/Core/models/project_model.dart';
import 'package:devfolio/Core/models/skill_model.dart';
import 'package:devfolio/Core/Storage/Local/UserDataService/user_data_service.dart';
import 'package:devfolio/Core/Storage/Local/UserDataService/user_data_base_service.dart';
import 'package:devfolio/Core/services/Subabase/subabase_services.dart';
import 'package:devfolio/Core/Utils/constants/local_storage_keys.dart';
import 'package:devfolio/Core/Utils/constants/class_tables.dart';

class PortfolioSectionData {
  final PersonalInfoModel? personalInfo;
  final List<SkillModel> skills;
  final List<EducationModel> education;
  final List<CertificationModel> certifications;
  final List<ExperienceModel> experience;
  final List<ProjectModel> projects;
  final DateTime lastUpdated;

  PortfolioSectionData({
    this.personalInfo,
    this.skills = const [],
    this.education = const [],
    this.certifications = const [],
    this.experience = const [],
    this.projects = const [],
    DateTime? lastUpdated,
  }) : lastUpdated = lastUpdated ?? DateTime.now();

  bool get hasPersonalInfo => personalInfo != null && 
      (personalInfo!.name?.isNotEmpty == true || 
       personalInfo!.title?.isNotEmpty == true ||
       personalInfo!.description?.isNotEmpty == true);

  bool get hasSkills => skills.isNotEmpty;
  bool get hasEducation => education.isNotEmpty;
  bool get hasCertifications => certifications.isNotEmpty;
  bool get hasExperience => experience.isNotEmpty;
  bool get hasProjects => projects.isNotEmpty;

  List<String> get availableSections {
    final sections = <String>[];
    if (hasPersonalInfo) sections.add('Personal Info');
    if (hasSkills) sections.add('Skills');
    if (hasEducation) sections.add('Education');
    if (hasCertifications) sections.add('Certifications');
    if (hasExperience) sections.add('Experience');
    if (hasProjects) sections.add('Projects');
    return sections;
  }

  PortfolioSectionData copyWith({
    PersonalInfoModel? personalInfo,
    List<SkillModel>? skills,
    List<EducationModel>? education,
    List<CertificationModel>? certifications,
    List<ExperienceModel>? experience,
    List<ProjectModel>? projects,
    DateTime? lastUpdated,
  }) {
    return PortfolioSectionData(
      personalInfo: personalInfo ?? this.personalInfo,
      skills: skills ?? this.skills,
      education: education ?? this.education,
      certifications: certifications ?? this.certifications,
      experience: experience ?? this.experience,
      projects: projects ?? this.projects,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }
}

class PortfolioDataService {
  static const Duration _cacheTimeout = Duration(minutes: 30);

  /// Fetch all portfolio data sections from Supabase with caching
  static Future<Either<String, PortfolioSectionData>> getAllPortfolioData({
    bool refresh = false,
    String? userId,
  }) async {
    try {
      final String userIdToUse = userId ?? UserDataBaseService.getUserDataId();
      
      if (userIdToUse.isEmpty) {
        return Left('User ID not found');
      }

      // Check cache first if not refreshing
      if (!refresh) {
        final cachedData = _getCachedData();
        if (cachedData != null && _isCacheValid(cachedData.lastUpdated)) {
          return Right(cachedData);
        }
      }

      // Fetch from Supabase
      final response = await SubabaseServices.get(
        table: ClassTables.portfolioData,
        filter: {'userId': userIdToUse},
      );

      if (!response.status || response.data == null || response.data.isEmpty) {
        return Left('No portfolio data found');
      }

      final portfolioData = response.data[0];
      
      // Parse each section
      final personalInfo = _parsePersonalInfo(portfolioData);
      final skills = _parseSkills(portfolioData);
      final education = _parseEducation(portfolioData);
      final certifications = _parseCertifications(portfolioData);
      final experience = _parseExperience(portfolioData);
      final projects = _parseProjects(portfolioData);

      final sectionData = PortfolioSectionData(
        personalInfo: personalInfo,
        skills: skills,
        education: education,
        certifications: certifications,
        experience: experience,
        projects: projects,
      );

      // Update local cache
      await _updateCache(sectionData);

      return Right(sectionData);
    } catch (e) {
      return Left('Failed to fetch portfolio data: $e');
    }
  }

  /// Get specific section data
  static Future<Either<String, T>> getSectionData<T>({
    required String sectionName,
    bool refresh = false,
    String? userId,
  }) async {
    try {
      final result = await getAllPortfolioData(refresh: refresh, userId: userId);
      
      return result.fold(
        (error) => Left(error),
        (data) {
          switch (sectionName.toLowerCase()) {
            case 'personalinfo':
              return data.personalInfo != null 
                  ? Right(data.personalInfo as T)
                  : Left('Personal info not found');
            case 'skills':
              return Right(data.skills as T);
            case 'education':
              return Right(data.education as T);
            case 'certifications':
              return Right(data.certifications as T);
            case 'experience':
              return Right(data.experience as T);
            case 'projects':
              return Right(data.projects as T);
            default:
              return Left('Unknown section: $sectionName');
          }
        },
      );
    } catch (e) {
      return Left('Failed to get section data: $e');
    }
  }

  // Private helper methods
  static PersonalInfoModel? _parsePersonalInfo(Map<String, dynamic> data) {
    try {
      final personalInfoData = data[ClassTables.personalInfo];
      if (personalInfoData != null && personalInfoData is Map) {
        return PersonalInfoModel.fromJson(
          Map<String, dynamic>.from(personalInfoData),
        );
      }
    } catch (e) {
      // Log error but don't fail the entire operation
    }
    return null;
  }

  static List<SkillModel> _parseSkills(Map<String, dynamic> data) {
    try {
      final skillsList = data['skills'] as List<dynamic>? ?? [];
      return skillsList
          .map((skill) => SkillModel.fromJson(Map<String, dynamic>.from(skill)))
          .toList();
    } catch (e) {
      return [];
    }
  }

  static List<EducationModel> _parseEducation(Map<String, dynamic> data) {
    try {
      final educationList = data['education'] as List<dynamic>? ?? [];
      return educationList
          .map((edu) => EducationModel.fromJson(Map<String, dynamic>.from(edu)))
          .toList();
    } catch (e) {
      return [];
    }
  }

  static List<CertificationModel> _parseCertifications(Map<String, dynamic> data) {
    try {
      final certsList = data[ClassTables.certifications] as List<dynamic>? ?? [];
      return certsList
          .map((cert) => CertificationModel.fromJson(Map<String, dynamic>.from(cert)))
          .toList();
    } catch (e) {
      return [];
    }
  }

  static List<ExperienceModel> _parseExperience(Map<String, dynamic> data) {
    try {
      final expList = data['experience'] as List<dynamic>? ?? [];
      return expList
          .map((exp) => ExperienceModel.fromJson(Map<String, dynamic>.from(exp)))
          .toList();
    } catch (e) {
      return [];
    }
  }

  static List<ProjectModel> _parseProjects(Map<String, dynamic> data) {
    try {
      final projectsList = data['projects'] as List<dynamic>? ?? [];
      return projectsList
          .map((project) => ProjectModel.fromJson(Map<String, dynamic>.from(project)))
          .toList();
    } catch (e) {
      return [];
    }
  }

  static PortfolioSectionData? _getCachedData() {
    try {
      final personalInfo = UserDataService.getMapField(LocalStorageKeys.personalInfo);
      final skills = UserDataService.getListData(LocalStorageKeys.skills);
      final education = UserDataService.getListData(LocalStorageKeys.education);
      final certificates = UserDataService.getListData(LocalStorageKeys.certificates);
      final experience = UserDataService.getListData(LocalStorageKeys.experience);
      final projects = UserDataService.getListData(LocalStorageKeys.projects);

      return PortfolioSectionData(
        personalInfo: personalInfo != null ? PersonalInfoModel.fromJson(personalInfo) : null,
        skills: skills.map((s) => SkillModel.fromJson(s)).toList(),
        education: education.map((e) => EducationModel.fromJson(e)).toList(),
        certifications: certificates.map((c) => CertificationModel.fromJson(c)).toList(),
        experience: experience.map((e) => ExperienceModel.fromJson(e)).toList(),
        projects: projects.map((p) => ProjectModel.fromJson(p)).toList(),
      );
    } catch (e) {
      return null;
    }
  }

  static bool _isCacheValid(DateTime lastUpdated) {
    return DateTime.now().difference(lastUpdated) < _cacheTimeout;
  }

  static Future<void> _updateCache(PortfolioSectionData data) async {
    try {
      if (data.personalInfo != null) {
        UserDataService.updateMapField(
          LocalStorageKeys.personalInfo,
          data.personalInfo!.toJson(),
        );
      }
      
      await UserDataService.updateListData(
        LocalStorageKeys.skills,
        data.skills.map((s) => s.toJson()).toList(),
      );
      
      await UserDataService.updateListData(
        LocalStorageKeys.education,
        data.education.map((e) => e.toJson()).toList(),
      );
      
      await UserDataService.updateListData(
        LocalStorageKeys.certificates,
        data.certifications.map((c) => c.toJson()).toList(),
      );
      
      await UserDataService.updateListData(
        LocalStorageKeys.experience,
        data.experience.map((e) => e.toJson()).toList(),
      );
      
      await UserDataService.updateListData(
        LocalStorageKeys.projects,
        data.projects.map((p) => p.toJson()).toList(),
      );
    } catch (e) {
      // Log error but don't fail the operation
    }
  }
}
