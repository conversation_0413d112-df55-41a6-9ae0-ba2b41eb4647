import '../../Data/portfolio_data_service.dart';

class PortfolioDisplayState {
  final PortfolioSectionData? portfolioData;
  final bool isLoading;
  final bool isRefreshing;
  final String? errorMessage;
  final List<String> availableSections;
  final int currentSectionIndex;
  final bool hasInitialized;

  PortfolioDisplayState({
    this.portfolioData,
    this.isLoading = false,
    this.isRefreshing = false,
    this.errorMessage,
    this.availableSections = const [],
    this.currentSectionIndex = 0,
    this.hasInitialized = false,
  });

  PortfolioDisplayState copyWith({
    PortfolioSectionData? portfolioData,
    bool? isLoading,
    bool? isRefreshing,
    String? errorMessage,
    List<String>? availableSections,
    int? currentSectionIndex,
    bool? hasInitialized,
  }) {
    return PortfolioDisplayState(
      portfolioData: portfolioData ?? this.portfolioData,
      isLoading: isLoading ?? this.isLoading,
      isRefreshing: isRefreshing ?? this.isRefreshing,
      errorMessage: errorMessage,
      availableSections: availableSections ?? this.availableSections,
      currentSectionIndex: currentSectionIndex ?? this.currentSectionIndex,
      hasInitialized: hasInitialized ?? this.hasInitialized,
    );
  }

  /// Check if any section has data
  bool get hasAnyData => availableSections.isNotEmpty;

  /// Check if personal info section has data
  bool get hasPersonalInfo => portfolioData?.personalInfo?.hasData == true;

  /// Check if skills section has data
  bool get hasSkills => portfolioData?.skills?.hasData == true && 
                       portfolioData?.skills?.data?.isNotEmpty == true;

  /// Check if education section has data
  bool get hasEducation => portfolioData?.education?.hasData == true && 
                          portfolioData?.education?.data?.isNotEmpty == true;

  /// Check if certifications section has data
  bool get hasCertifications => portfolioData?.certifications?.hasData == true && 
                               portfolioData?.certifications?.data?.isNotEmpty == true;

  /// Check if experience section has data
  bool get hasExperience => portfolioData?.experience?.hasData == true && 
                           portfolioData?.experience?.data?.isNotEmpty == true;

  /// Check if projects section has data
  bool get hasProjects => portfolioData?.projects?.hasData == true && 
                         portfolioData?.projects?.data?.isNotEmpty == true;

  /// Get section loading state
  bool isSectionLoading(String sectionName) {
    switch (sectionName.toLowerCase()) {
      case 'personal':
        return portfolioData?.personalInfo?.isLoading == true;
      case 'skills':
        return portfolioData?.skills?.isLoading == true;
      case 'education':
        return portfolioData?.education?.isLoading == true;
      case 'certifications':
        return portfolioData?.certifications?.isLoading == true;
      case 'experience':
        return portfolioData?.experience?.isLoading == true;
      case 'projects':
        return portfolioData?.projects?.isLoading == true;
      default:
        return false;
    }
  }

  /// Get section error state
  bool isSectionError(String sectionName) {
    switch (sectionName.toLowerCase()) {
      case 'personal':
        return portfolioData?.personalInfo?.hasError == true;
      case 'skills':
        return portfolioData?.skills?.hasError == true;
      case 'education':
        return portfolioData?.education?.hasError == true;
      case 'certifications':
        return portfolioData?.certifications?.hasError == true;
      case 'experience':
        return portfolioData?.experience?.hasError == true;
      case 'projects':
        return portfolioData?.projects?.hasError == true;
      default:
        return false;
    }
  }

  /// Get section error message
  String? getSectionErrorMessage(String sectionName) {
    switch (sectionName.toLowerCase()) {
      case 'personal':
        return portfolioData?.personalInfo?.errorMessage;
      case 'skills':
        return portfolioData?.skills?.errorMessage;
      case 'education':
        return portfolioData?.education?.errorMessage;
      case 'certifications':
        return portfolioData?.certifications?.errorMessage;
      case 'experience':
        return portfolioData?.experience?.errorMessage;
      case 'projects':
        return portfolioData?.projects?.errorMessage;
      default:
        return null;
    }
  }
}
