import '../../Data/portfolio_data_service.dart';

class PortfolioDisplayState {
  final PortfolioSectionData? portfolioData;
  final bool isLoading;
  final bool isRefreshing;
  final String? errorMessage;
  final List<String> availableSections;
  final int currentSectionIndex;
  final bool hasInitialized;
  final Map<String, bool> sectionLoadingStates;
  final Map<String, String?> sectionErrors;

  PortfolioDisplayState({
    this.portfolioData,
    this.isLoading = false,
    this.isRefreshing = false,
    this.errorMessage,
    this.availableSections = const [],
    this.currentSectionIndex = 0,
    this.hasInitialized = false,
    this.sectionLoadingStates = const {},
    this.sectionErrors = const {},
  });

  PortfolioDisplayState copyWith({
    PortfolioSectionData? portfolioData,
    bool? isLoading,
    bool? isRefreshing,
    String? errorMessage,
    List<String>? availableSections,
    int? currentSectionIndex,
    bool? hasInitialized,
    Map<String, bool>? sectionLoadingStates,
    Map<String, String?>? sectionErrors,
    bool clearError = false,
  }) {
    return PortfolioDisplayState(
      portfolioData: portfolioData ?? this.portfolioData,
      isLoading: isLoading ?? this.isLoading,
      isRefreshing: isRefreshing ?? this.isRefreshing,
      errorMessage: clearError ? null : (errorMessage ?? this.errorMessage),
      availableSections: availableSections ?? this.availableSections,
      currentSectionIndex: currentSectionIndex ?? this.currentSectionIndex,
      hasInitialized: hasInitialized ?? this.hasInitialized,
      sectionLoadingStates: sectionLoadingStates ?? this.sectionLoadingStates,
      sectionErrors: sectionErrors ?? this.sectionErrors,
    );
  }

  bool get hasData => portfolioData != null;
  bool get hasError => errorMessage != null;
  bool get isEmpty => hasInitialized && (portfolioData == null || availableSections.isEmpty);
  
  bool isSectionLoading(String sectionName) => sectionLoadingStates[sectionName] ?? false;
  String? getSectionError(String sectionName) => sectionErrors[sectionName];
  bool hasSectionError(String sectionName) => sectionErrors[sectionName] != null;
}
